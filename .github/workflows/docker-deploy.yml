name: Docker Build and Deploy

on:
  push:
    branches:
      - master

env:
  DOCKER_IMAGE: moviebox-api
  DOCKER_TAG: latest

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          push: true
          tags: ${{ secrets.DOCKER_USERNAME }}/${{ env.DOCKER_IMAGE }}:${{ env.DOCKER_TAG }}

      - name: Deploy to VPS
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.VPS_HOST }}
          username: ${{ secrets.VPS_USERNAME }}
          key: ${{ secrets.VPS_SSH_KEY }}
          script: |
            # Create .env file
            cat > .env << EOL
            SUPABASE_URL=${{ secrets.SUPABASE_URL }}
            SUPABASE_KEY=${{ secrets.SUPABASE_KEY }}
            REDIS_URL=redis://redis:6379
            KAGGLE_USERNAME=${{ secrets.KAGGLE_USERNAME }}
            KAGGLE_KEY=${{ secrets.KAGGLE_KEY }}
            EMBEDDINGS_MODEL=sentence-transformers/all-MiniLM-L6-v2
            EMBEDDINGS_MODEL_REVISION=8b3219a92973c328a8e22fadcfa821b5dc75636a
            EOL

            # Create docker-compose.yml file
            cat > docker-compose.yml << EOL
            version: '3.8'

            services:
              api:
                image: ${{ secrets.DOCKER_USERNAME }}/${{ env.DOCKER_IMAGE }}:${{ env.DOCKER_TAG }}
                ports:
                  - '8000:8000'
                environment:
                  - REDIS_URL=redis://redis:6379
                  - SUPABASE_URL=\${SUPABASE_URL}
                  - SUPABASE_KEY=\${SUPABASE_KEY}
                  - EMBEDDINGS_MODEL=sentence-transformers/all-MiniLM-L6-v2
                  - EMBEDDINGS_MODEL_REVISION=8b3219a92973c328a8e22fadcfa821b5dc75636a
                depends_on:
                  - redis
                volumes:
                  - milvus_data:/app/data/milvus:rw
                user: root

              redis:
                image: redis:7-alpine
                ports:
                  - '6379:6379'
                volumes:
                  - redis_data:/data

            volumes:
              redis_data:
              milvus_data:
            EOL

            # Create data directory with proper permissions
            mkdir -p data/milvus
            chmod 777 data/milvus

            # Install Docker Compose if not already installed
            if ! command -v docker-compose &> /dev/null; then
              sudo curl -L "https://github.com/docker/compose/releases/download/v2.24.6/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
              sudo chmod +x /usr/local/bin/docker-compose
            fi

            # Clean up old images
            docker image prune -f

            # Pull the latest image
            docker pull ${{ secrets.DOCKER_USERNAME }}/${{ env.DOCKER_IMAGE }}:${{ env.DOCKER_TAG }}

            # Stop and remove existing containers
            docker-compose down || true

            # Start the services using docker-compose
            docker-compose up -d
